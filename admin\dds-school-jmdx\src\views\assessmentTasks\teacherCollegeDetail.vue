<template>
  <DT-View>
    <div class="assessment-detail-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <el-button type="text" @click="goBack" class="back-btn">
            <i class="el-icon-arrow-left"></i>
            返回
          </el-button>
          <h2 class="page-title">{{ assessmentInfo.name }}</h2>
        </div>
      </div>

      <!-- 方案信息 -->
      <div class="scheme-info">
        <div class="info-item">
          <span class="label">数据确认时间：</span>
          <span class="value">{{ assessmentInfo.confirmTime }}</span>
        </div>
        <div class="info-item">
          <span class="label">方案介绍：</span>
          <span class="value">{{ assessmentInfo.introduction }}</span>
        </div>
        <div class="info-item">
          <span class="label">方案要求：</span>
          <span class="value">{{ assessmentInfo.requirements }}</span>
        </div>
        <div class="info-item">
          <span class="label">附件：</span>
          <span class="value">
            <el-link type="primary" :underline="false">Word.doc</el-link>
            <el-link type="primary" :underline="false" style="margin-left: 10px">Excel.xlsx</el-link>
          </span>
        </div>
      </div>

      <!-- 得分概览 -->
      <div class="score-overview">
        <h3 class="section-title">{{ collegeName }} 得分概览</h3>
        <div class="score-cards">
          <div class="score-card total-score">
            <div class="score-card-left">
              <div class="score-value">{{ totalScore }}</div>
              <div class="score-label">总得分</div>
            </div>
            <div class="progress-card">
              <div class="progress-value">确认进度 {{ confirmedCount }}/{{ totalCount }}</div>
            </div>
          </div>
          <div class="score-operator">=</div>
          <div class="score-card basic-score">
            <div class="score-value">{{ basicScore }}</div>
            <div class="score-label">学院基础性指标得分</div>
          </div>
          <div class="score-operator">+</div>
          <div class="score-card development-score">
            <div class="score-value">{{ developmentScore }}</div>
            <div class="score-label">学院发展性指标得分</div>
          </div>
          <div class="score-operator">+</div>
          <div class="score-card individual-score">
            <div class="score-value">{{ individualScore }}</div>
            <div class="score-label">学院下个人发展性指标得分</div>
          </div>
        </div>
      </div>

      <!-- 确认进度 -->
      <div class="confirmation-progress">
        <div class="progress-header">
          <div class="header-left">
            <h3 class="section-title">确认进度</h3>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="goToConfirm">去确认</el-button>
          </div>
        </div>

        <!-- 标签页 -->
        <div class="tabs-container">
          <div class="filter-bar">
            <span class="filter-label">是否显示已确认指标：</span>
            <el-switch
              v-model="showConfirmed"
              active-text="显示"
              inactive-text="隐藏"
              @change="handleShowConfirmedChange"
            />
          </div>
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="学院基础性指标 (5/15)" name="basic">
              <div class="tab-content">
                <!-- 基础性指标表格 -->
                <el-table
                  :data="filteredBasicData"
                  style="width: 100%"
                  border
                  :span-method="objectSpanMethod"
                  :header-cell-style="{
                    background: '#f5f7fa',
                    color: '#333',
                    fontWeight: '600',
                  }"
                >
                  <el-table-column prop="primaryIndicator" label="一级指标" width="150" />
                  <el-table-column prop="secondaryIndicator" label="二级指标" min-width="200" />
                  <el-table-column
                    prop="suggestedScore"
                    label="建议分值"
                    width="150"
                    align="center"
                    sortable
                  />
                  <el-table-column
                    prop="score"
                    label="得分"
                    width="100"
                    align="center"
                    sortable
                  />
                  <el-table-column
                    prop="status"
                    label="确认情况"
                    width="120"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-tag
                        :type="scope.row.status === '已确认' ? 'success' : 'warning'"
                        size="small"
                      >
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" align="center">
                    <template slot-scope="scope">
                      <el-button type="text" @click="viewDetail(scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <el-tab-pane label="学院发展性指标 (5/40)" name="development">
              <div class="tab-content">
                <!-- 发展性指标表格 -->
                <el-table
                  :data="filteredDevelopmentData"
                  style="width: 100%"
                  :span-method="objectSpanMethod"
                  :header-cell-style="{
                    background: '#f5f7fa',
                    color: '#333',
                    fontWeight: '600',
                  }"
                >
                  <el-table-column prop="primaryIndicator" label="一级指标" width="150" />
                  <el-table-column prop="secondaryIndicator" label="二级指标" min-width="200" />
                  <el-table-column
                    prop="suggestedScore"
                    label="建议分值"
                    width="150"
                    align="center"
                    sortable
                  />
                  <el-table-column
                    prop="score"
                    label="得分"
                    width="100"
                    align="center"
                    sortable
                  />
                  <el-table-column
                    prop="status"
                    label="确认情况"
                    width="120"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-tag
                        :type="scope.row.status === '已确认' ? 'success' : 'warning'"
                        size="small"
                      >
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" align="center">
                    <template slot-scope="scope">
                      <el-button type="text" @click="viewDetail(scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <el-tab-pane label="学院下个人发展性指标 (5/40)" name="individual">
              <div class="tab-content">
                <!-- 个人发展性指标表格 -->
                <el-table
                  :data="filteredIndividualData"
                  style="width: 100%"
                  :span-method="objectSpanMethod"
                  :header-cell-style="{
                    background: '#f5f7fa',
                    color: '#333',
                    fontWeight: '600',
                  }"
                >
                  <el-table-column prop="primaryIndicator" label="一级指标" width="150" />
                  <el-table-column prop="secondaryIndicator" label="二级指标" min-width="200" />
                  <el-table-column
                    prop="suggestedScore"
                    label="建议分值"
                    width="150"
                    align="center"
                    sortable
                  />
                  <el-table-column
                    prop="score"
                    label="得分"
                    width="100"
                    align="center"
                    sortable
                  />
                  <el-table-column
                    prop="status"
                    label="确认情况"
                    width="120"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-tag
                        :type="scope.row.status === '已确认' ? 'success' : 'warning'"
                        size="small"
                      >
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" align="center">
                    <template slot-scope="scope">
                      <el-button type="text" @click="viewDetail(scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </DT-View>
</template>

<script>
export default {
  name: 'TeacherCollegeDetail',
  data() {
    return {
      activeTab: 'basic',
      showConfirmed: true,
      collegeName: '信息学院',
      assessmentInfo: {
        name: '2024年度积分方案',
        confirmTime: '2022-12-02 至 2021-12-10',
        introduction: '本方案旨在全面评估学院和教师的工作表现和贡献，通过科学合理的指标体系，促进学院和教师的全面发展。',
        requirements: '请按照相关规定和要求进行数据填报和确认，确保数据的准确性和完整性。',
      },
      totalScore: 9000,
      basicScore: 200,
      developmentScore: 200,
      individualScore: 200,
      confirmedCount: 10,
      totalCount: 50,
      basicData: [
        {
          id: 1,
          primaryIndicator: '立德树人',
          secondaryIndicator: '党建、理论学习活动情况',
          suggestedScore: 10,
          score: 20,
          status: '已确认',
        },
        {
          id: 2,
          primaryIndicator: '立德树人',
          secondaryIndicator: '落实课程思政情况',
          suggestedScore: 20,
          score: 20,
          status: '已确认',
        },
        {
          id: 3,
          primaryIndicator: '立德树人',
          secondaryIndicator: '担任班主任、本科生导师',
          suggestedScore: 10,
          score: 10,
          status: '已确认',
        },
        {
          id: 4,
          primaryIndicator: '立德树人',
          secondaryIndicator: '指导学生就业创业',
          suggestedScore: 10,
          score: 10,
          status: '已确认',
        },
        {
          id: 5,
          primaryIndicator: '教育教学',
          secondaryIndicator: '申报教改项目情况',
          suggestedScore: 10,
          score: 10,
          status: '未确认',
        },
        {
          id: 6,
          primaryIndicator: '教育教学',
          secondaryIndicator: '申报教学类奖项情况',
          suggestedScore: 10,
          score: 10,
          status: '未确认',
        },
        {
          id: 7,
          primaryIndicator: '教育教学',
          secondaryIndicator: '参与教材编写情况',
          suggestedScore: 10,
          score: 10,
          status: '未确认',
        },
        {
          id: 8,
          primaryIndicator: '教育教学',
          secondaryIndicator: '指导学生毕业设计情况',
          suggestedScore: 10,
          score: 10,
          status: '未确认',
        },
      ],
      developmentData: [
        {
          id: 9,
          primaryIndicator: '科研创新',
          secondaryIndicator: '国家级科研项目',
          suggestedScore: 50,
          score: 50,
          status: '已确认',
        },
        {
          id: 10,
          primaryIndicator: '科研创新',
          secondaryIndicator: '省部级科研项目',
          suggestedScore: 30,
          score: 30,
          status: '已确认',
        },
        {
          id: 11,
          primaryIndicator: '科研创新',
          secondaryIndicator: '发表高水平论文',
          suggestedScore: 20,
          score: 20,
          status: '已确认',
        },
        {
          id: 12,
          primaryIndicator: '社会服务',
          secondaryIndicator: '产学研合作项目',
          suggestedScore: 15,
          score: 15,
          status: '已确认',
        },
        {
          id: 13,
          primaryIndicator: '社会服务',
          secondaryIndicator: '社会培训项目',
          suggestedScore: 10,
          score: 10,
          status: '未确认',
        },
      ],
      individualData: [
        {
          id: 14,
          primaryIndicator: '个人发展',
          secondaryIndicator: '教师个人获奖情况',
          suggestedScore: 20,
          score: 20,
          status: '已确认',
        },
        {
          id: 15,
          primaryIndicator: '个人发展',
          secondaryIndicator: '教师个人荣誉称号',
          suggestedScore: 15,
          score: 15,
          status: '已确认',
        },
        {
          id: 16,
          primaryIndicator: '个人发展',
          secondaryIndicator: '教师个人技能提升',
          suggestedScore: 10,
          score: 10,
          status: '已确认',
        },
        {
          id: 17,
          primaryIndicator: '个人发展',
          secondaryIndicator: '教师个人培训情况',
          suggestedScore: 8,
          score: 8,
          status: '未确认',
        },
      ],
    };
  },
  computed: {
    filteredBasicData() {
      if (this.showConfirmed) {
        return this.basicData;
      }
      return this.basicData.filter((item) => item.status === '未确认');
    },
    filteredDevelopmentData() {
      if (this.showConfirmed) {
        return this.developmentData;
      }
      return this.developmentData.filter((item) => item.status === '未确认');
    },
    filteredIndividualData() {
      if (this.showConfirmed) {
        return this.individualData;
      }
      return this.individualData.filter((item) => item.status === '未确认');
    },
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },

    handleTabClick(tab) {
      console.log('切换到标签页:', tab.name);
    },

    handleShowConfirmedChange(value) {
      console.log('显示已确认指标:', value);
    },

    viewDetail(row) {
      console.log('查看详情:', row);
      // 这里可以跳转到具体的详情页面
    },

    goToConfirm() {
      console.log('去确认');
      this.$router.push({ 
        name: 'DataConfirm',
        query: {
          userType: 'college'
        }
      });
    },

    // 单元格合并方法
    objectSpanMethod({ rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 一级指标列合并
        const currentData = this.getCurrentTableData();
        const primaryIndicators = currentData.map(item => item.primaryIndicator);
        
        if (rowIndex === 0) {
          // 第一行，计算合并的行数
          const currentIndicator = primaryIndicators[rowIndex];
          let count = 1;
          for (let i = rowIndex + 1; i < primaryIndicators.length; i++) {
            if (primaryIndicators[i] === currentIndicator) {
              count++;
            } else {
              break;
            }
          }
          return {
            rowspan: count,
            colspan: 1
          };
        } else {
          // 非第一行，检查是否需要合并
          const currentIndicator = primaryIndicators[rowIndex];
          const prevIndicator = primaryIndicators[rowIndex - 1];
          
          if (currentIndicator === prevIndicator) {
            // 与上一行相同，隐藏当前行
            return {
              rowspan: 0,
              colspan: 0
            };
          } else {
            // 与上一行不同，计算合并的行数
            let count = 1;
            for (let i = rowIndex + 1; i < primaryIndicators.length; i++) {
              if (primaryIndicators[i] === currentIndicator) {
                count++;
              } else {
                break;
              }
            }
            return {
              rowspan: count,
              colspan: 1
            };
          }
        }
      }
    },

    // 获取当前表格数据
    getCurrentTableData() {
      switch (this.activeTab) {
        case 'basic':
          return this.filteredBasicData;
        case 'development':
          return this.filteredDevelopmentData;
        case 'individual':
          return this.filteredIndividualData;
        default:
          return this.filteredBasicData;
      }
    },
  },

  created() {
    // 从路由参数获取考核ID
    const assessmentId = this.$route.params.id;
    const assessmentName = this.$route.query.name;
    const assessmentType = this.$route.query.type;

    if (assessmentName) {
      this.assessmentInfo.name = assessmentName;
    }

    // 根据考核类型设置学院名称
    if (assessmentType === '教师考核') {
      this.collegeName = '教师';
    } else if (assessmentType === '学院考核') {
      this.collegeName = '信息学院';
    }

    console.log('考核ID:', assessmentId);
    console.log('考核名称:', assessmentName);
    console.log('考核类型:', assessmentType);
  },
};
</script>

<style lang="scss" scoped>
.assessment-detail-container {
}



.assessment-detail-container {
  scrollbar-width: thin;
  scrollbar-color: #c0c4cc #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 0 20px 0;
  border-bottom: 1px solid #e4e7ed;
  
  .header-left {
    display: flex;
    align-items: center;
  }
}

.back-btn {
  margin-right: 16px;
  font-size: 16px;
  color: #409eff;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.scheme-info {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    font-weight: 600;
    color: #333;
    min-width: 120px;
  }

  .value {
    color: #666;
    flex: 1;
  }
}

.score-overview {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.score-cards {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.score-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  min-width: 120px;

  &.total-score {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    display: flex;
    align-items: center;
  }

  &.basic-score {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  &.development-score {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  &.individual-score {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .score-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .score-label {
    font-size: 14px;
    opacity: 0.9;
  }
}

.score-operator {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.progress-card {
  border-left: 1px solid #e9ecef;
  padding: 16px;
  margin-left: 16px;
  text-align: center;

  .progress-value {
    font-size: 16px;
    font-weight: 600;
    color: #fff;
  }
}

.confirmation-progress {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tabs-container {
  position: relative;
  
  .el-tabs__header {
    margin-bottom: 20px;
  }
}

.filter-bar {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 6px;

  .filter-label {
    margin-right: 12px;
    color: #333;
    font-weight: 500;
  }
}
</style> 